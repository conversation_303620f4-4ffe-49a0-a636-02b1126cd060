/**
 * Test file for Send Donation Call functionality
 * This file tests the email sending feature in EligibleDonorsPage
 */

import bloodDonationService from '../services/bloodDonationService';

// Test function to verify the API endpoint
export const testSendDonationCall = async () => {
  console.log('🧪 Testing Send Donation Call API...');
  
  try {
    // Call the sendDonationCall method
    const result = await bloodDonationService.sendDonationCall();
    
    console.log('✅ Send Donation Call test PASSED');
    console.log('📧 API Response:', result);
    
    return {
      success: true,
      message: 'Email sending API is working correctly',
      data: result
    };
    
  } catch (error) {
    console.log('❌ Send Donation Call test FAILED');
    console.error('Error details:', error);
    
    // Check if it's a network/API issue or actual error
    if (error.response?.status === 404) {
      return {
        success: false,
        message: 'API endpoint not found - Backend may not be running',
        error: error.message
      };
    } else if (error.response?.status === 500) {
      return {
        success: false,
        message: 'Server error - Check backend logs',
        error: error.message
      };
    } else {
      return {
        success: false,
        message: 'Network or connection error',
        error: error.message
      };
    }
  }
};

// Test function to verify the API endpoint URL
export const verifyApiEndpoint = () => {
  console.log('🔍 Verifying API endpoint configuration...');
  
  const expectedEndpoint = '/Appointment/send-donation-call';
  console.log(`📍 Expected endpoint: POST ${expectedEndpoint}`);
  
  // Check if the service method exists
  if (typeof bloodDonationService.sendDonationCall === 'function') {
    console.log('✅ Service method exists');
    return {
      success: true,
      message: 'Service method is properly configured',
      endpoint: expectedEndpoint
    };
  } else {
    console.log('❌ Service method not found');
    return {
      success: false,
      message: 'Service method sendDonationCall is not defined',
      endpoint: expectedEndpoint
    };
  }
};

// Run all tests
export const runAllTests = async () => {
  console.log('🚀 Starting Send Donation Call tests...');
  console.log('=' .repeat(50));
  
  // Test 1: Verify endpoint configuration
  const endpointTest = verifyApiEndpoint();
  console.log('Test 1 - Endpoint Configuration:', endpointTest.success ? 'PASS' : 'FAIL');
  
  // Test 2: Test actual API call
  const apiTest = await testSendDonationCall();
  console.log('Test 2 - API Call:', apiTest.success ? 'PASS' : 'FAIL');
  
  console.log('=' .repeat(50));
  console.log('📊 Test Summary:');
  console.log(`- Endpoint Configuration: ${endpointTest.success ? '✅ PASS' : '❌ FAIL'}`);
  console.log(`- API Call: ${apiTest.success ? '✅ PASS' : '❌ FAIL'}`);
  
  if (!apiTest.success) {
    console.log('💡 Troubleshooting tips:');
    console.log('1. Make sure backend server is running');
    console.log('2. Check if the endpoint /api/Appointment/send-donation-call exists');
    console.log('3. Verify authentication tokens if required');
    console.log('4. Check network connectivity');
  }
  
  return {
    endpointTest,
    apiTest,
    overall: endpointTest.success && apiTest.success
  };
};

// Export for use in browser console
if (typeof window !== 'undefined') {
  window.testSendDonationCall = {
    testSendDonationCall,
    verifyApiEndpoint,
    runAllTests
  };
  
  console.log('🔧 Test functions available in window.testSendDonationCall');
  console.log('Usage: window.testSendDonationCall.runAllTests()');
}
