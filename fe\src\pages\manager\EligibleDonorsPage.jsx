import React, { useState, useEffect } from "react";
import {
  Table,
  Modal,
  Button,
  Select,
  Input,
  Space,
  Tag,
  Tooltip,
  Card,
  Steps,
  Row,
  Col,
  Switch,
  Badge,
  Divider,
} from "antd";
import { toast } from "../../utils/toastUtils";
import {
  EyeOutlined,
  MailOutlined,
  PhoneOutlined,
  EnvironmentOutlined,
  ReloadOutlined,
  TableOutlined,
  AppstoreOutlined,
  UserOutlined,
  HeartOutlined,
  CheckCircleOutlined,
  ClockCircleOutlined,
} from "@ant-design/icons";
import ManagerLayout from "../../components/manager/ManagerLayout";
import PageHeader from "../../components/manager/PageHeader";
import ProcessWorkflowModal, {
  DONATION_STATUSES,
} from "../../components/shared/ProcessWorkflowModal";
import GeolibService from "../../services/geolibService";
import bloodDonationService from "../../services/bloodDonationService";
import userInfoService from "../../services/userInfoService";
import "../../styles/pages/manager/EligibleDonorsPage.scss";
import "../../styles/components/PageHeader.scss";

const { Option } = Select;
const { Search } = Input;

const EligibleDonorsPage = () => {
  const [donors, setDonors] = useState([]);
  const [filteredDonors, setFilteredDonors] = useState([]);
  const [loading, setLoading] = useState(true);
  const [selectedDonor, setSelectedDonor] = useState(null);
  const [detailModalVisible, setDetailModalVisible] = useState(false);
  const [processModalVisible, setProcessModalVisible] = useState(false);
  const [viewMode, setViewMode] = useState("table"); // 'table' or 'card'
  const [filters, setFilters] = useState({
    bloodType: "all",
    maxDistance: 50,
    searchText: "",
    sortBy: "distance",
    status: "all",
  });

  // Hàm kiểm tra eligibility (84 ngày)
  const isEligibleToDonate = (lastDonationDate) => {
    if (!lastDonationDate) return true;

    const lastDonation = new Date(lastDonationDate);
    const currentDate = new Date();
    const daysDifference = Math.floor(
      (currentDate - lastDonation) / (1000 * 60 * 60 * 24)
    );

    return daysDifference >= 84;
  };

  // Format ngày theo DD/MM/YYYY (từ cột DonationDate trong bảng Appointment)
  const formatDate = (dateString) => {
    if (!dateString) return "Chưa hiến";
    return new Date(dateString).toLocaleDateString("vi-VN");
  };

  // Get status info for display (simplified version for table rendering)
  const getStatusInfo = (status) => {
    const statusMap = {
      [DONATION_STATUSES.REGISTERED]: {
        text: "Đã đăng ký",
        color: "#1890ff",
        icon: <UserOutlined />,
        step: 0,
      },
      [DONATION_STATUSES.HEALTH_CHECKED]: {
        text: "Đã khám sức khỏe cơ bản",
        color: "#52c41a",
        icon: <CheckCircleOutlined />,
        step: 1,
      },
      [DONATION_STATUSES.BLOOD_TAKEN]: {
        text: "Đã lấy máu",
        color: "#722ed1",
        icon: <HeartOutlined />,
        step: 2,
      },
      [DONATION_STATUSES.BLOOD_TESTED]: {
        text: "Đã xét nghiệm máu",
        color: "#fa8c16",
        icon: <ClockCircleOutlined />,
        step: 3,
      },
      [DONATION_STATUSES.STORED]: {
        text: "Đã nhập kho",
        color: "#13c2c2",
        icon: <CheckCircleOutlined />,
        step: 4,
      },
    };
    return statusMap[status] || statusMap[DONATION_STATUSES.REGISTERED];
  };

  useEffect(() => {
    loadEligibleDonors();
  }, []);

  useEffect(() => {
    applyFilters();
  }, [donors, filters]);

  // Refresh data function
  const refreshData = () => {
    loadEligibleDonors();
  };

  const loadEligibleDonors = async () => {
    setLoading(true);
    try {
      // Get all blood donation submissions from API
      const donationsData = await bloodDonationService.getAllBloodDonationSubmissions();

      // Get unique user IDs to avoid duplicate API calls
      const uniqueUserIds = [...new Set(donationsData.map(d => d.userId).filter(Boolean))];

      // Fetch user info for all unique users at once
      const userInfoCache = {};
      await Promise.all(
        uniqueUserIds.map(async (userId) => {
          try {
            const userInfo = await userInfoService.getUserInfo(userId);
            userInfoCache[userId] = userInfo;
          } catch (error) {
            userInfoCache[userId] = null; // Mark as failed
          }
        })
      );

      // Transform API data to eligible donors format with detailed info from Information API
      const transformedDonors = donationsData.map((donation) => {
        const userInfo = userInfoCache[donation.userId] || {};

        // Calculate age from dateOfBirth if available
        const calculateAge = (dateOfBirth) => {
          if (!dateOfBirth) return null;
          const today = new Date();
          const birthDate = new Date(dateOfBirth);
          let age = today.getFullYear() - birthDate.getFullYear();
          const monthDiff = today.getMonth() - birthDate.getMonth();
          if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < birthDate.getDate())) {
            age--;
          }
          return age;
        };

        // Build comprehensive donor object with Information API data
        let donor = {
          id: donation.appointmentId || donation.id,
          name: userInfo.fullName || userInfo.name || donation.donorName || donation.name || `User ${donation.userId}`,
          // Combine BloodGroup + RhType from Information API with proper formatting
          bloodType: (() => {
            if (userInfo.bloodGroup && userInfo.rhType) {
              // Clean and format blood group (remove "Rh" if present)
              const cleanBloodGroup = userInfo.bloodGroup.replace(/Rh/gi, '').trim();
              const cleanRhType = userInfo.rhType.trim();
              return `${cleanBloodGroup}${cleanRhType}`;
            }
            return donation.bloodType || "O+";
          })(),
          phone: userInfo.phoneNumber || userInfo.phone || donation.donorPhone || donation.phone || "",
          email: userInfo.email || donation.donorEmail || donation.email || "",
          coordinates: {
            lat: userInfo.latitude || donation.latitude || 10.7769,
            lng: userInfo.longitude || donation.longitude || 106.7009
          },
          address: userInfo.address || donation.address || "Chưa có địa chỉ",
          lastDonationDate: donation.donationDate || donation.lastDonationDate || donation.registrationDate,
          totalDonations: donation.totalDonations || 1,
          healthStatus: donation.healthStatus || "good",
          age: calculateAge(userInfo.dateOfBirth) || donation.age || 25,
          weight: userInfo.weight || donation.weight || 60,
          height: userInfo.height || donation.height || 170,
          notes: donation.notes || "",
          donationStatus: mapApiStatusToComponentStatus(donation.status),
          appointmentDate: donation.appointmentDate || donation.date,
          timeSlot: donation.timeSlot || "morning",
          userId: donation.userId,
          process: donation.process || 1, // Add process field from API
          // Use distance from database (Information API) - show "no info" if not available
          distance: userInfo.distance !== undefined && userInfo.distance !== null && userInfo.distance !== 0
            ? parseFloat(userInfo.distance)
            : null, // null indicates no data in database

          // Additional detailed information from Information API
          donorDetails: {
            dateOfBirth: userInfo.dateOfBirth || null,
            gender: userInfo.gender || null,
            identityCard: userInfo.identityCard || null,
            occupation: userInfo.occupation || null,
            emergencyContact: userInfo.emergencyContact || null,
            medicalHistory: userInfo.medicalHistory || null,
          }
        };



        return donor;
      });

      // Filter only eligible donors (84 days since last donation) and completed donation process (process = 5)
      const eligibleDonors = transformedDonors
        .filter((donor) =>
          isEligibleToDonate(donor.lastDonationDate) &&
          donor.process === 5 // Only show donors who completed the full donation process (process = 5: Nhập kho)
        )
        .map((donor) => ({
          ...donor,
          formattedLastDonation: formatDate(donor.lastDonationDate),
        }));

      setDonors(eligibleDonors);

    } catch (error) {
      console.error("Error loading eligible donors:", error);

      // Show user-friendly error message
      if (error.response?.status === 404) {
        toast.warning("API endpoint chưa sẵn sàng. Đang sử dụng dữ liệu mẫu.");
      } else {
        toast.error("Có lỗi xảy ra khi tải danh sách người hiến máu!");
      }




      // Filter eligible donors and add distance (try from database first, fallback to calculation)
      const eligibleDonors = await Promise.all(
        mockDonors
          .filter((donor) =>
            isEligibleToDonate(donor.lastDonationDate) &&
            donor.process === 5 // Only show donors who completed the full donation process (process = 5: Nhập kho)
          )
          .map(async (donor) => {
            let distance = GeolibService.getDistanceToHospital(donor.coordinates);

            // Try to get distance from database if userId exists
            if (donor.userId) {
              try {
                const userInfo = await userInfoService.getUserInfo(donor.userId);
                distance = parseDistanceFromDB(userInfo?.distance, distance);
              } catch (error) {
                // Use calculated distance as fallback
              }
            }

            return {
              ...donor,
              distance,
              formattedLastDonation: formatDate(donor.lastDonationDate),
            };
          })
      );

      setDonors(eligibleDonors);

    } finally {
      setLoading(false);
    }
  };

  // Helper function to parse and validate distance from database
  const parseDistanceFromDB = (rawDistance, fallbackDistance) => {
    if (rawDistance === null || rawDistance === undefined) {
      return fallbackDistance;
    }

    let parsedDistance = rawDistance;

    // Handle string values (remove 'km' or 'm' if present and convert to number)
    if (typeof parsedDistance === 'string') {
      parsedDistance = parseFloat(parsedDistance.replace(/km|m/gi, '').trim());
    }

    // Ensure it's a valid number
    if (!isNaN(parsedDistance) && parsedDistance >= 0) {
      return Math.round(parsedDistance * 100) / 100; // Round to 2 decimal places
    } else {
      return fallbackDistance;
    }
  };

  // Helper function to map API status to component status
  const mapApiStatusToComponentStatus = (apiStatus) => {
    const statusMap = {
      // Numeric statuses
      0: DONATION_STATUSES.REGISTERED,
      1: DONATION_STATUSES.HEALTH_CHECKED,
      2: DONATION_STATUSES.BLOOD_TAKEN,
      3: DONATION_STATUSES.BLOOD_TESTED,
      4: DONATION_STATUSES.STORED,

      // String statuses
      "registered": DONATION_STATUSES.REGISTERED,
      "pending": DONATION_STATUSES.REGISTERED,
      "health_checked": DONATION_STATUSES.HEALTH_CHECKED,
      "blood_taken": DONATION_STATUSES.BLOOD_TAKEN,
      "donated": DONATION_STATUSES.BLOOD_TAKEN,
      "blood_tested": DONATION_STATUSES.BLOOD_TESTED,
      "stored": DONATION_STATUSES.STORED,
    };

    return statusMap[apiStatus] || DONATION_STATUSES.REGISTERED;
  };

  const applyFilters = () => {
    let filtered = [...donors];

    if (filters.bloodType !== "all") {
      filtered = filtered.filter(
        (donor) => donor.bloodType === filters.bloodType
      );
    }

    if (filters.maxDistance) {
      filtered = filtered.filter(
        (donor) => donor.distance <= filters.maxDistance
      );
    }

    if (filters.status !== "all") {
      filtered = filtered.filter(
        (donor) => donor.donationStatus === filters.status
      );
    }

    if (filters.searchText) {
      const searchLower = filters.searchText.toLowerCase();
      filtered = filtered.filter(
        (donor) =>
          donor.name.toLowerCase().includes(searchLower) ||
          donor.phone.includes(filters.searchText) ||
          donor.email.toLowerCase().includes(searchLower)
      );
    }

    filtered.sort((a, b) => {
      switch (filters.sortBy) {
        case "distance":
          return a.distance - b.distance;
        case "lastDonation":
          return new Date(b.lastDonationDate) - new Date(a.lastDonationDate);
        case "name":
          return a.name.localeCompare(b.name);
        case "status":
          return (
            getStatusInfo(a.donationStatus).step -
            getStatusInfo(b.donationStatus).step
          );
        default:
          return 0;
      }
    });

    setFilteredDonors(filtered);
  };

  const handleViewDetails = async (donor) => {
    try {
      // Try to get more detailed user info if userId is available
      if (donor.userId) {
        try {
          // Use userInfoService to get complete user information including distance
          const userInfo = await userInfoService.getUserInfo(donor.userId);

          // Merge user info with donor data, prioritizing database distance
          const finalDistance = parseDistanceFromDB(userInfo?.distance, donor.distance);

          const detailedDonor = {
            ...donor,
            ...userInfo,
            // Keep original donor data as priority for donation-specific fields
            id: donor.id,
            donationStatus: donor.donationStatus,
            formattedLastDonation: donor.formattedLastDonation,
            // Use processed distance
            distance: finalDistance,
          };

          setSelectedDonor(detailedDonor);
        } catch (userError) {
          // Fallback to blood donation service
          try {
            const userInfo = await bloodDonationService.getUserInfo(donor.userId);

            const detailedDonor = {
              ...donor,
              ...userInfo,
              id: donor.id,
              donationStatus: donor.donationStatus,
              distance: donor.distance,
              formattedLastDonation: donor.formattedLastDonation,
            };

            setSelectedDonor(detailedDonor);
          } catch (fallbackError) {
            setSelectedDonor(donor);
          }
        }
      } else {
        setSelectedDonor(donor);
      }

      setDetailModalVisible(true);
    } catch (error) {
      console.error('Error viewing donor details:', error);
      setSelectedDonor(donor);
      setDetailModalVisible(true);
    }
  };

  const handleViewProcess = (donor) => {
    setSelectedDonor(donor);
    setProcessModalVisible(true);
  };

  const handleUpdateStatus = async (donorId, newStatus) => {
    try {
      // Update status using blood donation service API
      await bloodDonationService.updateBloodDonationSubmissionStatus(donorId, newStatus);

      // Update local state
      setDonors((prev) =>
        prev.map((donor) =>
          donor.id === donorId ? { ...donor, donationStatus: newStatus } : donor
        )
      );
      toast.success("✅ Cập nhật trạng thái thành công!");
    } catch (error) {
      console.error("Error updating status:", error);
      toast.error("❌ Có lỗi xảy ra khi cập nhật trạng thái!");
    }
  };

  const handleSendEmail = async (donor) => {
    try {
      // TODO: Replace with actual API call - POST /api/donors/send-email
      const emailData = {
        to: donor.email,
        subject: "Kêu gọi hiến máu - Bệnh viện Ánh Dương",
        content: `Kính chào ${donor.name}, Bệnh viện Ánh Dương hiện đang cần máu nhóm ${donor.bloodType} để cứu chữa bệnh nhân.`,
      };

      console.log("Sending email:", emailData);
      toast.success(`Đã gửi email kêu gọi hiến máu đến ${donor.name}!`);
    } catch (error) {
      console.error("Error sending email:", error);
      toast.error("Có lỗi xảy ra khi gửi email!");
    }
  };

  const handleSendBulkDonationCall = async () => {
    try {
      // Show confirmation modal
      Modal.confirm({
        title: "Gửi email kêu gọi hiến máu",
        content: (
          <div>
            <p>Bạn có chắc chắn muốn gửi email kêu gọi hiến máu đến tất cả những người đã hoàn thành quy trình hiến máu và đủ điều kiện hiến lại?</p>
            <p style={{ color: '#666', fontSize: '0.9rem' }}>
              Hệ thống sẽ gửi email đến những người đã hoàn thành quy trình hiến máu (trạng thái "Đã nhập kho"), đã hiến máu cách đây ít nhất 84 ngày và phù hợp với các bộ lọc hiện tại.
            </p>
            <p style={{ color: '#1890ff', fontWeight: 'bold' }}>
              Số người sẽ nhận email: {filteredDonors.length} người
            </p>
          </div>
        ),
        okText: "Gửi email",
        cancelText: "Hủy",
        okType: "primary",
        icon: <MailOutlined style={{ color: '#1890ff' }} />,
        onOk: async () => {
          try {
            // Call API to send donation call emails
            await bloodDonationService.sendDonationCall();
            toast.success(`✅ Đã gửi email kêu gọi hiến máu đến ${filteredDonors.length} người đã hoàn thành quy trình hiến máu!`);
          } catch (error) {
            console.error("Error sending bulk donation call:", error);
            if (error.response?.status === 404) {
              toast.warning("⚠️ API gửi email chưa sẵn sàng. Vui lòng thử lại sau.");
            } else {
              toast.error("❌ Có lỗi xảy ra khi gửi email kêu gọi hiến máu!");
            }
          }
        }
      });
    } catch (error) {
      console.error("Error in bulk donation call:", error);
      toast.error("❌ Có lỗi xảy ra!");
    }
  };

  const handleSendDonationCall = async () => {
    try {
      // Show confirmation modal
      Modal.confirm({
        title: "📧 Gửi email kêu gọi hiến máu",
        content: (
          <div>
            <p style={{ fontSize: '16px', marginBottom: '16px' }}>
              Bạn có chắc chắn muốn gửi email kêu gọi hiến máu đến tất cả những người đủ điều kiện hiến máu?
            </p>

            <div style={{
              background: '#f6ffed',
              border: '1px solid #b7eb8f',
              borderRadius: '8px',
              padding: '12px',
              marginBottom: '16px'
            }}>
              <h4 style={{ color: '#389e0d', margin: '0 0 8px 0' }}>📋 Tiêu chí gửi email:</h4>
              <ul style={{ margin: 0, paddingLeft: '20px', color: '#52c41a' }}>
                <li>✅ Đã hoàn thành quy trình hiến máu (trạng thái "Đã nhập kho")</li>
                <li>✅ Đã hiến máu cách đây ít nhất 84 ngày</li>
                <li>✅ Phù hợp với các bộ lọc hiện tại</li>
              </ul>
            </div>

            <div style={{
              background: '#e6f7ff',
              border: '1px solid #91d5ff',
              borderRadius: '8px',
              padding: '12px',
              textAlign: 'center'
            }}>
              <div style={{ fontSize: '24px', fontWeight: 'bold', color: '#1890ff', marginBottom: '4px' }}>
                {filteredDonors.length} người
              </div>
              <div style={{ color: '#0050b3', fontWeight: '500' }}>
                sẽ nhận được email kêu gọi hiến máu
              </div>
            </div>
          </div>
        ),
        okText: "Gửi email",
        cancelText: "Hủy",
        okType: "primary",
        icon: <MailOutlined style={{ color: '#1890ff' }} />,
        width: 600,
        onOk: async () => {
          try {
            // Call API to send donation call emails
            await bloodDonationService.sendDonationCall();
            toast.success(`✅ Đã gửi email kêu gọi hiến máu đến tất cả người đủ điều kiện!`);
          } catch (error) {
            console.error("Error sending donation call:", error);
            if (error.message?.includes('API endpoint chưa sẵn sàng')) {
              toast.warning("⚠️ API gửi email chưa sẵn sàng. Vui lòng thử lại sau.");
            } else if (error.response?.status === 404) {
              toast.warning("⚠️ API gửi email chưa sẵn sàng. Vui lòng thử lại sau.");
            } else {
              toast.error("❌ Có lỗi xảy ra khi gửi email kêu gọi hiến máu!");
            }
          }
        }
      });
    } catch (error) {
      console.error("Error in donation call:", error);
      toast.error("❌ Có lỗi xảy ra!");
    }
  };

  const handleStoreBlood = async (donorId) => {
    try {
      // Update status to STORED using API
      await handleUpdateStatus(donorId, DONATION_STATUSES.STORED);
      toast.success("📦 Đã nhập kho thành công!");
    } catch (error) {
      console.error("Error storing blood:", error);
      toast.error("❌ Có lỗi xảy ra khi nhập kho!");
    }
  };

  const getBloodTypeColor = () => {
    // Use consistent color for all blood types (same as O+)
    return "#f50";
  };

  const columns = [
    {
      title: "STT",
      key: "index",
      width: 60,
      align: "center",
      render: (_, __, index) => (
        <span style={{ fontWeight: 600, color: "#666" }}>
          {index + 1}
        </span>
      ),
    },
    {
      title: "Tên người hiến",
      dataIndex: "name",
      key: "name",
      width: 180,
      sorter: (a, b) => a.name.localeCompare(b.name),
      render: (text, record) => (
        <div>
          <div style={{ fontWeight: 600 }}>{text}</div>
          <div style={{ fontSize: "0.8rem", color: "#666" }}>
            ID: {record.userId}
          </div>
        </div>
      ),
    },
    {
      title: "Nhóm máu",
      dataIndex: "bloodType",
      key: "bloodType",
      width: 110,
      align: "center",
      sorter: (a, b) => a.bloodType.localeCompare(b.bloodType),
      render: (bloodType) => {
        // Clean up blood type display (remove any extra characters)
        const cleanBloodType = bloodType?.replace(/Rh/gi, '').trim() || "O+";
        return (
          <Tag
            color={getBloodTypeColor(cleanBloodType)}
            style={{
              fontWeight: "bold",
              fontSize: "0.85rem",
              padding: "4px 8px",
              borderRadius: "6px",
              minWidth: "45px",
              textAlign: "center"
            }}
          >
            {cleanBloodType}
          </Tag>
        );
      },
    },

    {
      title: "Khoảng cách",
      dataIndex: "distance",
      key: "distance",
      width: 120,
      align: "center",
      sorter: (a, b) => {
        // Handle null values in sorting - put null values at the end
        if (a.distance === null && b.distance === null) return 0;
        if (a.distance === null) return 1;
        if (b.distance === null) return -1;
        return a.distance - b.distance;
      },
      render: (distance) => {
        if (distance === null || distance === undefined) {
          return (
            <span
              style={{
                color: "#999",
                fontStyle: "italic",
                fontWeight: "500"
              }}
              title="Chưa có thông tin khoảng cách trong database"
            >
              Chưa có thông tin
            </span>
          );
        }

        return (
          <span
            style={{
              color:
                distance <= 10
                  ? "#52c41a"
                  : distance <= 20
                    ? "#faad14"
                    : "#ff4d4f",
              fontWeight: "600"
            }}
            title={`Khoảng cách từ database: ${distance} km`}
          >
            {GeolibService.formatDistance(distance)}
          </span>
        );
      },
    },
    {
      title: (
        <Tooltip title="Ngày hiến máu cuối cùng từ cột DonationDate trong bảng Appointment">
          <span>Lần hiến cuối</span>
        </Tooltip>
      ),
      dataIndex: "formattedLastDonation",
      key: "lastDonation",
      width: 140,
      align: "center",
      sorter: (a, b) =>
        new Date(b.lastDonationDate) - new Date(a.lastDonationDate),
      render: (formattedDate, record) => (
        <div>
          <div style={{ fontWeight: 600, color: "#1890ff" }}>
            {formattedDate}
          </div>

        </div>
      ),
    },
    {
      title: "Số điện thoại",
      dataIndex: "phone",
      key: "phone",
      width: 140,
      render: (phone) => (
        <a href={`tel:${phone}`} style={{ color: "#1890ff" }}>
          <PhoneOutlined /> {phone}
        </a>
      ),
    },
    {
      title: "Hành động",
      key: "actions",
      width: 220,
      align: "center",
      render: (_, record) => (
        <Space>
          <Tooltip title="Xem chi tiết">
            <Button
              type="primary"
              icon={<EyeOutlined />}
              size="small"
              onClick={() => handleViewDetails(record)}
            />
          </Tooltip>
          <Tooltip title="Quy trình hiến máu">
            <Button
              type="default"
              icon={<ClockCircleOutlined />}
              size="small"
              onClick={() => handleViewProcess(record)}
            />
          </Tooltip>

        </Space>
      ),
    },
  ];

  return (
    <ManagerLayout pageTitle="Người đã hoàn thành hiến máu - Đủ điều kiện hiến lại">
      <div className="donors-content">
        <PageHeader
          title="Người đã hoàn thành hiến máu - Đủ điều kiện hiến lại"
          icon={UserOutlined}
          extra={
            <Space>
              <Button
                type="default"
                icon={<MailOutlined />}
                onClick={handleSendBulkDonationCall}
                disabled={filteredDonors.length === 0}
                style={{
                  background: '#fff2e8',
                  borderColor: '#ffbb96',
                  color: '#d4380d'
                }}
              >
                Gửi email kêu gọi
              </Button>
              <Button
                type="primary"
                icon={<ReloadOutlined />}
                onClick={refreshData}
                loading={loading}
              >
                Làm mới
              </Button>
            </Space>
          }
        />

        {/* Filters */}
        <div className="filters-section">
          <Space wrap size="large">
            <div className="filter-group">
              <label>Tìm kiếm:</label>
              <Search
                placeholder="Tên, số điện thoại, email..."
                value={filters.searchText}
                onChange={(e) =>
                  setFilters((prev) => ({
                    ...prev,
                    searchText: e.target.value,
                  }))
                }
                style={{ width: 250 }}
                allowClear
              />
            </div>

            <div className="filter-group">
              <label>Nhóm máu:</label>
              <Select
                value={filters.bloodType}
                onChange={(value) =>
                  setFilters((prev) => ({ ...prev, bloodType: value }))
                }
                style={{ width: 120 }}
              >
                <Option value="all">Tất cả</Option>
                <Option value="O+">O+</Option>
                <Option value="O-">O-</Option>
                <Option value="A+">A+</Option>
                <Option value="A-">A-</Option>
                <Option value="B+">B+</Option>
                <Option value="B-">B-</Option>
                <Option value="AB+">AB+</Option>
                <Option value="AB-">AB-</Option>
              </Select>
            </div>

            <div className="filter-group">
              <label>Khoảng cách:</label>
              <Select
                value={filters.maxDistance}
                onChange={(value) =>
                  setFilters((prev) => ({ ...prev, maxDistance: value }))
                }
                style={{ width: 120 }}
              >
                <Option value={5}>≤ 5km</Option>
                <Option value={10}>≤ 10km</Option>
                <Option value={20}>≤ 20km</Option>
                <Option value={50}>≤ 50km</Option>
                <Option value={100}>≤ 100km</Option>
              </Select>
            </div>



            <div className="filter-group">
              <label>Sắp xếp:</label>
              <Select
                value={filters.sortBy}
                onChange={(value) =>
                  setFilters((prev) => ({ ...prev, sortBy: value }))
                }
                style={{ width: 150 }}
              >
                <Option value="distance">Khoảng cách</Option>
                <Option value="lastDonation">Lần hiến cuối</Option>
                <Option value="name">Tên A-Z</Option>

              </Select>
            </div>

            {/* Send Donation Call Button */}
            <div className="filter-group">
              <Button
                type="primary"
                icon={<MailOutlined />}
                onClick={handleSendDonationCall}
                disabled={filteredDonors.length === 0}
                size="large"
                style={{
                  background: 'linear-gradient(135deg, #ff6b6b, #ee5a24)',
                  borderColor: '#ee5a24',
                  color: '#fff',
                  fontWeight: 'bold',
                  boxShadow: '0 4px 12px rgba(238, 90, 36, 0.3)',
                  borderRadius: '8px',
                  marginTop: '24px'
                }}
              >
                Gửi email kêu gọi
              </Button>
            </div>
          </Space>
        </div>

        {/* Donors Display */}
        {viewMode === "table" ? (
          <div className="table-section">
            <Table
              columns={columns}
              dataSource={filteredDonors}
              rowKey="id"
              loading={loading}
              pagination={{
                pageSize: 10,
                showSizeChanger: true,
                showQuickJumper: true,
                showTotal: (total, range) =>
                  `${range[0]}-${range[1]} của ${total} người hiến`,
              }}
              scroll={{ x: 1260 }}
              size="middle"
            />
          </div>
        ) : (
          <div className="cards-section">
            <Row gutter={[16, 16]}>
              {filteredDonors.map((donor) => {
                const statusInfo = getStatusInfo(donor.donationStatus);
                return (
                  <Col xs={24} sm={12} lg={8} xl={6} key={donor.id}>
                    <Card
                      className="donor-card"
                      hoverable
                      actions={[
                        <Tooltip title="Xem chi tiết" key="detail">
                          <EyeOutlined
                            onClick={() => handleViewDetails(donor)}
                          />
                        </Tooltip>,
                        <Tooltip title="Quy trình hiến máu" key="process">
                          <ClockCircleOutlined
                            onClick={() => handleViewProcess(donor)}
                          />
                        </Tooltip>,
                        <Tooltip title="Gửi email" key="email">
                          <MailOutlined
                            onClick={() => handleSendEmail(donor)}
                          />
                        </Tooltip>,
                      ]}
                    >
                      <div className="card-header">
                        <div className="donor-name">{donor.name}</div>
                        <Tag
                          color={getBloodTypeColor(donor.bloodType?.replace(/Rh/gi, '').trim() || "O+")}
                          className="blood-type-tag"
                        >
                          {donor.bloodType?.replace(/Rh/gi, '').trim() || "O+"}
                        </Tag>
                      </div>

                      <div className="card-content">
                        <div className="status-section">
                          <Badge
                            color={statusInfo.color}
                            text={statusInfo.text}
                            className="status-badge"
                          />
                        </div>

                        <div className="info-item">
                          <EnvironmentOutlined className="info-icon" />
                          <span className="distance-text">
                            {GeolibService.formatDistance(donor.distance)}
                          </span>
                        </div>

                        <div className="info-item">
                          <PhoneOutlined className="info-icon" />
                          <a href={`tel:${donor.phone}`} className="phone-link">
                            {donor.phone}
                          </a>
                        </div>

                        <div className="info-item">
                          <ClockCircleOutlined className="info-icon" />
                          <span>
                            Lần hiến cuối: {donor.formattedLastDonation}
                          </span>
                        </div>
                      </div>
                    </Card>
                  </Col>
                );
              })}
            </Row>

            {filteredDonors.length === 0 && !loading && (
              <div className="empty-state">
                <UserOutlined className="empty-icon" />
                <h3>Không tìm thấy người hiến nào</h3>
                <p>Thử điều chỉnh bộ lọc để xem thêm kết quả</p>
              </div>
            )}
          </div>
        )}

        {/* Detail Modal */}
        <Modal
          title={`Chi tiết người hiến: ${selectedDonor?.name}`}
          open={detailModalVisible}
          onCancel={() => setDetailModalVisible(false)}
          footer={[
            <Button key="close" onClick={() => setDetailModalVisible(false)}>
              Đóng
            </Button>,

          ]}
          width={600}
        >
          {selectedDonor && (
            <div className="donor-details">
              <div className="detail-section">
                <h4>Thông tin cơ bản</h4>
                <div className="detail-grid">
                  <div className="detail-item">
                    <label>Họ tên:</label>
                    <span>{selectedDonor.name}</span>
                  </div>
                  <div className="detail-item">
                    <label>ID người dùng:</label>
                    <span>{selectedDonor.userId}</span>
                  </div>
                  <div className="detail-item">
                    <label>Nhóm máu:</label>
                    <Tag
                      color={getBloodTypeColor(selectedDonor.bloodType?.replace(/Rh/gi, '').trim() || "O+")}
                      style={{ fontWeight: "bold", fontSize: "0.9rem" }}
                    >
                      {selectedDonor.bloodType?.replace(/Rh/gi, '').trim() || "O+"}
                    </Tag>
                  </div>
                  {selectedDonor.donorDetails?.dateOfBirth && (
                    <div className="detail-item">
                      <label>Ngày sinh:</label>
                      <span>{new Date(selectedDonor.donorDetails.dateOfBirth).toLocaleDateString("vi-VN")}</span>
                    </div>
                  )}
                  <div className="detail-item">
                    <label>Tuổi:</label>
                    <span>{selectedDonor.age} tuổi</span>
                  </div>
                  {selectedDonor.donorDetails?.gender && (
                    <div className="detail-item">
                      <label>Giới tính:</label>
                      <span>{selectedDonor.donorDetails.gender === 'Male' ? 'Nam' : selectedDonor.donorDetails.gender === 'Female' ? 'Nữ' : selectedDonor.donorDetails.gender}</span>
                    </div>
                  )}
                  <div className="detail-item">
                    <label>Cân nặng:</label>
                    <span>{selectedDonor.weight} kg</span>
                  </div>
                  {selectedDonor.height && (
                    <div className="detail-item">
                      <label>Chiều cao:</label>
                      <span>{selectedDonor.height} cm</span>
                    </div>
                  )}
                  {selectedDonor.donorDetails?.identityCard && (
                    <div className="detail-item">
                      <label>CCCD/CMND:</label>
                      <span>{selectedDonor.donorDetails.identityCard}</span>
                    </div>
                  )}
                  {selectedDonor.donorDetails?.occupation && (
                    <div className="detail-item">
                      <label>Nghề nghiệp:</label>
                      <span>{selectedDonor.donorDetails.occupation}</span>
                    </div>
                  )}
                </div>
              </div>

              <div className="detail-section">
                <h4>Thông tin liên hệ</h4>
                <div className="detail-grid">
                  <div className="detail-item">
                    <label>Số điện thoại:</label>
                    <a href={`tel:${selectedDonor.phone}`}>
                      <PhoneOutlined /> {selectedDonor.phone}
                    </a>
                  </div>
                  <div className="detail-item">
                    <label>Email:</label>
                    <a href={`mailto:${selectedDonor.email}`}>
                      <MailOutlined /> {selectedDonor.email}
                    </a>
                  </div>
                  <div className="detail-item full-width">
                    <label>Địa chỉ:</label>
                    <span>
                      <EnvironmentOutlined /> {selectedDonor.address}
                    </span>
                  </div>
                  <div className="detail-item">
                    <label>Khoảng cách :</label>
                    <span style={{
                      fontWeight: "600",
                      color: selectedDonor.distance === null ? "#999" : "inherit",
                      fontStyle: selectedDonor.distance === null ? "italic" : "normal"
                    }}>
                      {selectedDonor.distance !== null
                        ? GeolibService.formatDistance(selectedDonor.distance)
                        : "Chưa có thông tin"
                      }
                    </span>
                  </div>
                </div>
              </div>



              {/* Thông tin y tế và liên hệ khẩn cấp */}
              {(selectedDonor.donorDetails?.emergencyContact || selectedDonor.donorDetails?.medicalHistory) && (
                <div className="detail-section">
                  <h4>Thông tin y tế & Liên hệ khẩn cấp</h4>
                  <div className="detail-grid">
                    {selectedDonor.donorDetails?.emergencyContact && (
                      <div className="detail-item full-width">
                        <label>Liên hệ khẩn cấp:</label>
                        <span>{selectedDonor.donorDetails.emergencyContact}</span>
                      </div>
                    )}
                    {selectedDonor.donorDetails?.medicalHistory && (
                      <div className="detail-item full-width">
                        <label>Tiền sử bệnh án:</label>
                        <span>{selectedDonor.donorDetails.medicalHistory}</span>
                      </div>
                    )}
                  </div>
                </div>
              )}

              {selectedDonor.notes && (
                <div className="detail-section">
                  <h4>Ghi chú</h4>
                  <p>{selectedDonor.notes}</p>
                </div>
              )}
            </div>
          )}
        </Modal>

        {/* Process Workflow Modal */}
        <ProcessWorkflowModal
          visible={processModalVisible}
          onCancel={() => setProcessModalVisible(false)}
          selectedItem={selectedDonor}
          onStoreBlood={handleStoreBlood}
          isManager={true}
          title="Quy trình hiến máu"
        />
      </div>
    </ManagerLayout>
  );
};

export default EligibleDonorsPage;
