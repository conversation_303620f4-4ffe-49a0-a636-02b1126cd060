# Tính năng Button Gửi Email Kêu Gọi Hiến Máu

## Tổng quan
Tính năng này cho phép quản lý gửi email kêu gọi hiến máu đến tất cả những người đủ điều kiện hiến máu chỉ với một cú nhấp chuột.

## Vị trí
- **Trang**: `EligibleDonorsPage` (`/fe/src/pages/manager/EligibleDonorsPage.jsx`)
- **Đường dẫn**: `/manager/eligible-donors`
- **Vị trí button**: <PERSON>ê<PERSON> cạnh filter "Sắp xếp" trong phần filters
- **Quyền truy cập**: Chỉ dành cho Manager

## Tính năng chính

### 1. Button Gửi Email Kêu Gọi
- **Tên**: "📧 Gửi email kêu gọi (X người)"
- **Kiểu**: Primary button với gradient màu đỏ cam
- **Vị trí**: Trong phần filters, bên cạnh dropdown "Sắp xếp"
- **Trạng thái**: Tự động disable khi không có người nào đủ điều kiện

### 2. Modal Xác Nhận
Khi nhấn button, hệ thống hiển thị modal xác nhận với:
- **Tiêu đề**: "📧 Gửi email kêu gọi hiến máu"
- **Nội dung**: 
  - Tiêu chí gửi email (84 ngày, hoàn thành quy trình, bộ lọc)
  - Số lượng người sẽ nhận email
- **Nút**: "Gửi email" / "Hủy"

### 3. Tiêu chí gửi email
- ✅ Đã hoàn thành quy trình hiến máu (trạng thái "Đã nhập kho")
- ✅ Đã hiến máu cách đây ít nhất 84 ngày
- ✅ Phù hợp với các bộ lọc hiện tại

## API Endpoint

### Gửi Email Kêu Gọi
```
POST /api/Appointment/send-donation-call
```

**Headers:**
```
Content-Type: application/json
Accept: */*
```

**Request Body:** Không có parameters

**Response:**
```
Status: 200 OK
Content-Type: text/plain; charset=utf-8
Body: "Đã gửi email kêu gọi hiến máu."
```

## Cách sử dụng

### Bước 1: Truy cập trang
1. Đăng nhập với quyền Manager
2. Vào menu "Quản lý người hiến" → "Người đủ điều kiện hiến lại"

### Bước 2: Áp dụng bộ lọc (tùy chọn)
1. **Tìm kiếm**: Nhập tên, số điện thoại, hoặc email
2. **Nhóm máu**: Chọn nhóm máu cụ thể hoặc "Tất cả"
3. **Khoảng cách**: Chọn khoảng cách tối đa (5km, 10km, 20km, 50km, 100km)
4. **Sắp xếp**: Chọn cách sắp xếp (Khoảng cách, Lần hiến cuối, Tên A-Z)

### Bước 3: Gửi email
1. Nhấn button "📧 Gửi email kêu gọi (X người)"
2. Đọc thông tin trong modal xác nhận
3. Nhấn "Gửi email" để xác nhận hoặc "Hủy" để hủy bỏ

### Bước 4: Xem kết quả
- **Thành công**: Hiển thị thông báo "✅ Đã gửi email kêu gọi hiến máu đến tất cả người đủ điều kiện!"
- **Lỗi**: Hiển thị thông báo lỗi tương ứng

## Thông báo

### Thành công
```
✅ Đã gửi email kêu gọi hiến máu đến tất cả người đủ điều kiện!
```

### Lỗi
- **API chưa sẵn sàng**: 
  ```
  ⚠️ API gửi email chưa sẵn sàng. Vui lòng thử lại sau.
  ```
- **Lỗi khác**: 
  ```
  ❌ Có lỗi xảy ra khi gửi email kêu gọi hiến máu!
  ```

## Files liên quan

### Core Files
- `fe/src/pages/manager/EligibleDonorsPage.jsx` - Trang chính chứa button
- `fe/src/services/bloodDonationService.js` - Service API

### Test Files
- `fe/src/test/donationCallTest.js` - Test tính năng

### Documentation
- `fe/docs/DONATION_CALL_BUTTON.md` - Tài liệu này

## Code Implementation

### Button Component
```jsx
<Button
  type="primary"
  icon={<MailOutlined />}
  onClick={handleSendDonationCall}
  disabled={filteredDonors.length === 0}
  size="large"
  style={{
    background: 'linear-gradient(135deg, #ff6b6b, #ee5a24)',
    borderColor: '#ee5a24',
    color: '#fff',
    fontWeight: 'bold',
    boxShadow: '0 4px 12px rgba(238, 90, 36, 0.3)',
    borderRadius: '8px',
    marginTop: '24px'
  }}
>
  📧 Gửi email kêu gọi ({filteredDonors.length} người)
</Button>
```

### API Service Method
```javascript
async sendDonationCall() {
  try {
    const response = await apiClient.post('/Appointment/send-donation-call');
    return response.data;
  } catch (error) {
    // Handle errors
    throw error;
  }
}
```

## Troubleshooting

### Button bị disable
- **Nguyên nhân**: Không có người nào đủ điều kiện theo bộ lọc hiện tại
- **Giải pháp**: Điều chỉnh bộ lọc hoặc kiểm tra dữ liệu

### Lỗi "API endpoint chưa sẵn sàng"
- **Nguyên nhân**: Backend chưa chạy hoặc endpoint chưa được implement
- **Giải pháp**: 
  1. Kiểm tra backend có chạy tại `https://localhost:7021`
  2. Xác nhận endpoint `/api/Appointment/send-donation-call` đã được implement
  3. Kiểm tra CORS settings

### Không có người nào trong danh sách
- **Nguyên nhân**: 
  - Bộ lọc quá nghiêm ngặt
  - Không có dữ liệu trong database
  - Điều kiện 84 ngày chưa được đáp ứng
- **Giải pháp**:
  1. Thử bộ lọc "Tất cả" cho nhóm máu
  2. Tăng khoảng cách tối đa
  3. Kiểm tra dữ liệu trong database

## Lưu ý quan trọng

1. **Điều kiện 84 ngày**: Chỉ gửi email cho người đã hiến cách đây ít nhất 84 ngày
2. **Quy trình hoàn thành**: Chỉ gửi cho người đã hoàn thành toàn bộ quy trình hiến máu
3. **Bộ lọc**: Email chỉ gửi cho những người phù hợp với bộ lọc hiện tại
4. **API Backend**: Cần đảm bảo backend đã implement endpoint tương ứng
5. **Quyền truy cập**: Chỉ Manager mới có thể sử dụng tính năng này
