import { useState, useEffect, useCallback, useRef } from "react";
import authService from "../services/authService";
import bloodDonationService from "../services/bloodDonationService";
import { bloodRequestService } from "../services/bloodRequestService";
import { DONATION_STATUS, REQUEST_STATUS } from "../constants/systemConstants";
import { toast } from "../utils/toastUtils";

const useActivityData = () => {
  const [activities, setActivities] = useState([]);
  const [loading, setLoading] = useState(true);
  const [filter, setFilter] = useState("all");
  const [searchId, setSearchId] = useState("");
  const isFirstMount = useRef(true);

  const currentUser = authService.getCurrentUser();

  // Get user blood type from profile - giữ nguyên logic gốc
  const getUserBloodType = useCallback(() => {
    try {
      const memberInfo = JSON.parse(localStorage.getItem("memberInfo") || "{}");
      const bloodGroup =
        memberInfo.bloodGroup || currentUser?.profile?.bloodGroup;
      const rhType = memberInfo.rhType || currentUser?.profile?.rhType;

      if (bloodGroup && rhType) {
        const rhSymbol =
          rhType === "Rh+" || rhType === "+"
            ? "+"
            : rhType === "Rh-" || rhType === "-"
              ? "-"
              : rhType;
        return `${bloodGroup}${rhSymbol}`;
      } else if (bloodGroup) {
        return bloodGroup;
      } else {
        return "Chưa xác định";
      }
    } catch (error) {
      console.error("Error getting user blood type:", error);
      return "Chưa xác định";
    }
  }, [currentUser]);

  // Helper function to map API status - cập nhật theo yêu cầu mới
  const mapApiStatusToDisplayStatus = useCallback((appointment) => {
    // Nếu chỉ truyền vào status đơn giản (backward compatibility)
    if (typeof appointment === "string" || typeof appointment === "number") {
      const statusMap = {
        0: DONATION_STATUS.REGISTERED,
        1: DONATION_STATUS.NOT_ELIGIBLE_HEALTH,
        2: DONATION_STATUS.HEALTH_CHECKED,
        3: DONATION_STATUS.NOT_ELIGIBLE_HEALTH,
        registered: DONATION_STATUS.REGISTERED,
        pending: DONATION_STATUS.REGISTERED,
        health_checked: DONATION_STATUS.HEALTH_CHECKED,
        not_eligible_health: DONATION_STATUS.NOT_ELIGIBLE_HEALTH,
        donated: DONATION_STATUS.DONATED,
        blood_tested: DONATION_STATUS.BLOOD_TESTED,
        not_eligible_test: DONATION_STATUS.NOT_ELIGIBLE_TEST,
        completed: DONATION_STATUS.COMPLETED,
        stored: DONATION_STATUS.STORED,
        approved: DONATION_STATUS.HEALTH_CHECKED,
        rejected: DONATION_STATUS.NOT_ELIGIBLE_HEALTH,
        cancelled: DONATION_STATUS.NOT_ELIGIBLE_HEALTH,
        fulfilled: REQUEST_STATUS.FULFILLED,
      };
      const lookupKey = typeof appointment === "number" ? appointment : appointment;
      return statusMap[lookupKey] || DONATION_STATUS.REGISTERED;
    }

    // Logic mới: sử dụng process và status để xác định trạng thái hiển thị
    const process = appointment.process || appointment.Process || 1;
    const status = appointment.status;

    // Nếu bị từ chối (status = false)
    if (status === false) {
      return DONATION_STATUS.NOT_ELIGIBLE_HEALTH;
    }

    // Dựa vào process để xác định trạng thái
    if (process === 5) {
      // Đã nhập kho → Hoàn thành
      return DONATION_STATUS.STORED;
    } else if (process >= 2) {
      // Từ khám sức khỏe trở đi → Chấp nhận
      return DONATION_STATUS.HEALTH_CHECKED;
    } else {
      // Mới đăng ký → Đã đăng ký
      return DONATION_STATUS.REGISTERED;
    }
  }, []);

  // Helper function to map blood request status - giữ nguyên logic gốc
  const mapBloodRequestStatusToDisplayStatus = useCallback((apiStatus) => {
    const statusMap = {
      // String status mapping
      pending: REQUEST_STATUS.PENDING,
      approved: REQUEST_STATUS.APPROVED,
      completed: REQUEST_STATUS.FULFILLED,
      rejected: REQUEST_STATUS.REJECTED,
      cancelled: REQUEST_STATUS.CANCELLED,
      canceled: REQUEST_STATUS.CANCELLED,

      // Numeric status mapping (correct according to bloodRequestUtils.js)
      0: REQUEST_STATUS.PENDING, // Chờ duyệt
      1: REQUEST_STATUS.APPROVED, // Đã duyệt
      2: REQUEST_STATUS.COMPLETED, // Hoàn thành (status 2 = completed)
      3: REQUEST_STATUS.REJECTED, // Từ chối
      4: REQUEST_STATUS.CANCELLED, // Đã hủy
    };

    const lookupKey = typeof apiStatus === "number" ? apiStatus : apiStatus;
    return statusMap[lookupKey] || REQUEST_STATUS.PENDING;
  }, []);

  // Load activity history - cập nhật để lấy lịch sử hiến máu
  const loadActivityHistory = useCallback(async () => {
    console.log("🔄 Starting loadActivityHistory...");
    setLoading(true);
    try {
      const userBloodType = getUserBloodType();


      // Lấy lịch sử hiến máu từ API
      let appointmentsData = [];
      try {
        console.log(
          "Fetching blood donation appointments for user:",
          currentUser.id
        );
        appointmentsData = await bloodDonationService.getAppointmentsByUser(
          currentUser.id
        );
        console.log(
          "Blood donation appointments loaded:",
          appointmentsData?.length || 0
        );
        console.log("📋 Appointments data:", appointmentsData);
      } catch (appointmentError) {
        console.error(
          "Error loading blood donation appointments:",
          appointmentError
        );
        appointmentsData = [];
      }

      // Lấy thông tin yêu cầu máu từ API
      let bloodRequestsData = [];
      try {
        const bloodRequestsResponse =
          await bloodRequestService.getBloodRequestsByUser(currentUser.id);
        bloodRequestsData = bloodRequestsResponse.success
          ? bloodRequestsResponse.data
          : [];
        console.log(
          "Blood requests loaded:",
          bloodRequestsData?.length || 0
        );
      } catch (requestError) {
        console.error("Error loading blood requests:", requestError);
        bloodRequestsData = [];
      }

      // Đảm bảo bloodRequestsData là array (có thể rỗng)
      if (!bloodRequestsData) {
        bloodRequestsData = [];
      }

      // Chuyển đổi dữ liệu từ API thành format hiển thị - giữ nguyên logic gốc
      const donationActivities = (appointmentsData || []).map(
        (appointment, index) => {
          const donationDate =
            appointment.AppointmentDate ||
            appointment.requestedDonationDate ||
            appointment.appointmentDate;

          const mappedStatus = mapApiStatusToDisplayStatus(appointment);
          console.log(`✅ Mapped status for appointment ${index + 1}:`, mappedStatus);
          console.log(`🔍 Raw appointment data for ${index + 1}:`, {
            process: appointment.process,
            Process: appointment.Process,
            status: appointment.status,
            Status: appointment.Status,
            rejectionReason: appointment.rejectionReason || appointment.RejectionReason,
            note: appointment.note || appointment.Note
          });

          return {
            id: appointment.appointmentId || appointment.id || `donation-${currentUser.id}-${index}-${Date.now()}`,
            appointmentId: appointment.appointmentId || appointment.id, // Thêm appointmentId riêng để search
            type: "donation",
            title: "Đặt lịch hiến máu",
            status: mapApiStatusToDisplayStatus(appointment),
            displayStatus: mapApiStatusToDisplayStatus(appointment), // Thêm displayStatus cho consistency
            // Thêm process và status gốc để sử dụng trong các component khác
            process: appointment.process !== undefined ? appointment.process :
              appointment.Process !== undefined ? appointment.Process : 1,
            rawStatus: appointment.status !== undefined ? appointment.status : appointment.Status,
            bloodType: (() => {
              // Priority 1: Get from Appointment table
              if (appointment.BloodGroup && appointment.RhType) {
                return `${appointment.BloodGroup}${appointment.RhType.replace('Rh', '')}`;
              }
              if (appointment.bloodGroup && appointment.rhType) {
                return `${appointment.bloodGroup}${appointment.rhType.replace('Rh', '')}`;
              }

              // Priority 2: Get from User profile (fallback to current logic)
              const userBloodTypeFormatted = `${userBloodType}${currentUser?.rhType
                ? currentUser.rhType.includes("+") ||
                  currentUser.rhType.includes("-")
                  ? currentUser.rhType.replace("Rh", "")
                  : currentUser.rhType
                : ""
                }`;

              // Check if we have valid blood type data
              if (userBloodType && currentUser?.rhType) {
                return userBloodTypeFormatted;
              }

              // No data available in both tables
              return "N/A";
            })(),
            quantity: appointment.quantity || "450ml",
            appointmentDate: donationDate,
            timeSlot:
              appointment.TimeSlot || appointment.timeSlot || "Chưa xác định",
            location:
              appointment.location ||
              "Bệnh viện Đa khoa Ánh Dương - Khoa Huyết học, Tầng 2",
            notes: appointment.Notes || appointment.notes || "",
            doctorNotes:
              appointment.DoctorNotes || appointment.doctorNotes || "",
            // Thêm rejection reason cho trường hợp bị từ chối
            rejectionReason: (appointment.status === false || appointment.Status === false) ?
              (appointment.RejectionReason || appointment.rejectionReason ||
                appointment.RejectReason || appointment.rejectReason ||
                appointment.Note || appointment.note ||
                appointment.Notes || appointment.notes ||
                appointment.DoctorNotes || appointment.doctorNotes || "") : "",
            weight: appointment.Weight || appointment.weight || 0,
            height: appointment.Height || appointment.height || 0,
            hasDonated: appointment.hasDonated,
            lastDonationDate:
              appointment.LastDonationDate || appointment.lastDonationDate,
            // Thông tin sức khỏe chi tiết từ bác sĩ
            healthCheck: {
              heartRate: appointment.HeartRate || appointment.heartRate || "",
              bloodPressure:
                appointment.BloodPressure || appointment.bloodPressure || "",
              hemoglobin:
                appointment.Hemoglobin || appointment.hemoglobin || "",
              temperature:
                appointment.Temperature || appointment.temperature || "",
              weight: appointment.Weight || appointment.weight || 0,
              height: appointment.Height || appointment.height || 0,
            },
            createdAt:
              appointment.CreatedAt ||
              appointment.createdAt ||
              appointment.requestedDonationDate,
            completedAt: appointment.completedAt || null,
            isCancelled:
              appointment.Cancel === 1 ||
              appointment.Cancel === true ||
              appointment.cancel === 1 ||
              appointment.cancel === true ||
              appointment.cancelled === true ||
              appointment.cancelled === 1,
            cancelledAt:
              appointment.CancelledAt || appointment.cancelledAt || null,
          };
        }
      );

      // Chuyển đổi dữ liệu blood requests - sử dụng trực tiếp dữ liệu từ API
      const requestActivities = (bloodRequestsData || []).map(
        (request, index) => {
          const requestId =
            request.requestId || request.id || `request-${index}`;

          // Tạo statusHistory từ thông tin thực tế từ API
          const statusHistory = [];

          // Luôn có bước tạo yêu cầu
          statusHistory.push({
            status: 0, // Pending
            notes: "Yêu cầu được tạo thành công",
          });

          // Logic xử lý theo status hiện tại
          if (request.status === 3) {
            // Nếu bị từ chối, chỉ thêm bước từ chối, không thêm các bước khác
            statusHistory.push({
              status: 3,
              notes:
                request.rejectionReason ||
                request.rejectedNotes ||
                request.note || // Field "note" từ doctor
                request.notes ||
                request.rejectReason ||
                request.rejectNote ||
                "Yêu cầu bị từ chối",
              updatedBy: request.rejectedBy || request.updatedBy || "Bác sĩ khoa huyết học",
            });
          } else {
            // Chỉ thêm các bước tiếp theo nếu KHÔNG bị từ chối
            if (request.status >= 1) {
              // Đã chấp nhận
              statusHistory.push({
                status: 1,
                notes:
                  request.acceptedNotes ||
                  "Yêu cầu đã được chấp nhận",
                updatedBy: request.acceptedBy || "Bác sĩ khoa huyết học",
              });
            }

            if (request.status >= 2) {
              // Hoàn thành
              statusHistory.push({
                status: 2,
                notes:
                  request.completedNotes || "Máu đã được xuất kho và hoàn thành",
                updatedBy: request.completedBy || "Manager",
              });
            }
          }

          return {
            id: requestId,
            requestId: request.requestId || request.id, // Thêm requestId riêng để search
            type: "request",
            title: "Yêu cầu nhận máu",
            status: request.status, // Sử dụng raw status (0,1,2,3) cho DetailedStatusTimeline
            displayStatus: mapBloodRequestStatusToDisplayStatus(request.status), // Mapped status cho UI khác
            statusHistory: statusHistory, // Thêm statusHistory vào object trả về
            bloodType: `${request.bloodGroup || request.bloodType}${request.rhType
              ? request.rhType.includes("+") || request.rhType.includes("-")
                ? request.rhType.replace("Rh", "")
                : request.rhType
              : ""
              }`,
            quantity: `${request.quantity || 0}${request.unit || "ml"}`,
            patientName: request.patientName,
            patientAge: request.age,
            patientGender: request.gender,
            relationship: request.relationship,
            hospitalName: request.facilityName || request.hospitalName,
            doctorName: request.doctorName,
            doctorPhone: request.doctorPhone,
            medicalCondition: request.reason || request.medicalCondition,
            urgency: request.urgency || "normal",
            notes: request.notes || request.note || "",
            rejectionReason: request.rejectionReason || request.note || request.rejectReason || "",
            createdAt: request.createdTime || request.createdAt,
            updatedAt:
              request.updatedTime || request.createdTime || request.createdAt,
            completedAt: request.completedAt || null,
            isCancelled:
              request.status === "cancelled" || request.status === "canceled",
            cancelledAt: request.cancelledAt || null,
          };
        }
      );

      // Kết hợp và sắp xếp - bao gồm cả donation appointments và blood requests
      const allActivities = [...donationActivities, ...requestActivities];
      allActivities.sort(
        (a, b) => new Date(b.createdAt) - new Date(a.createdAt)
      );

      console.log("Total activities loaded:", {
        donations: donationActivities.length,
        requests: requestActivities.length,
        total: allActivities.length,
      });

      console.log("Setting activities to state...");
      setActivities(allActivities);
      console.log("Activities set successfully");

      // Ẩn thông báo loading filter nếu có
      toast.remove('filter-loading');
    } catch (error) {
      console.error("Error loading activity history:", error);
      setActivities([]);
      toast.remove('filter-loading');
    } finally {
      setLoading(false);
    }
  }, [
    currentUser,
    getUserBloodType,
    mapApiStatusToDisplayStatus,
    mapBloodRequestStatusToDisplayStatus,
  ]);

  // Cancel appointment - giữ nguyên logic gốc
  const handleCancelAppointment = useCallback(
    async (appointmentId) => {
      if (!window.confirm("Bạn có chắc chắn muốn hủy lịch hẹn này không?")) {
        return;
      }

      try {
        setLoading(true);
        console.log("Cancelling appointment:", appointmentId);
        await bloodDonationService.cancelAppointment(appointmentId);
        console.log("Appointment cancelled successfully");

        toast.success("Đã hủy lịch hẹn thành công!");

        console.log("Reloading activity history...");
        await loadActivityHistory();
        console.log("Activity history reloaded");
      } catch (error) {
        console.error("Error cancelling appointment:", error);
        toast.error("Có lỗi xảy ra khi hủy lịch hẹn. Vui lòng thử lại.");
      } finally {
        setLoading(false);
      }
    },
    [loadActivityHistory]
  );

  // Custom setFilter function để reload data khi filter thay đổi
  const handleFilterChange = useCallback((newFilter) => {
    console.log("Changing filter from", filter, "to", newFilter);

    // Hiển thị thông báo loading ngắn
    const filterLabels = {
      all: "Tất cả hoạt động",
      donations: "Hiến máu",
      requests: "Yêu cầu máu"
    };

    const loadingToastId = toast.loading(`Đang tải ${filterLabels[newFilter] || newFilter}...`);

    setFilter(newFilter);
    // Data sẽ được reload tự động thông qua useEffect
  }, [filter]);

  // Filter activities - thêm filter theo ID
  const getFilteredActivities = useCallback(() => {
    let filtered = activities;

    // Filter theo loại hoạt động
    switch (filter) {
      case "donations":
        filtered = filtered.filter((a) => a.type === "donation");
        break;
      case "requests":
        filtered = filtered.filter((a) => a.type === "request");
        break;
      default:
        // Giữ nguyên tất cả
        break;
    }

    // Filter theo ID nếu có searchId
    if (searchId && searchId.trim()) {
      const searchTerm = searchId.trim().toLowerCase();
      filtered = filtered.filter((activity) => {
        // Tìm theo appointmentId hoặc requestId
        const appointmentId = activity.appointmentId?.toString().toLowerCase() || "";
        const requestId = activity.requestId?.toString().toLowerCase() || "";
        const activityId = activity.id?.toString().toLowerCase() || "";

        return appointmentId.includes(searchTerm) ||
          requestId.includes(searchTerm) ||
          activityId.includes(searchTerm);
      });
    }

    return filtered;
  }, [activities, filter, searchId]);

  // Statistics - giữ nguyên logic gốc
  const donationCount = activities.filter((a) => a.type === "donation").length;
  const requestCount = activities.filter((a) => a.type === "request").length;
  const completedCount = activities.filter((a) =>
    [
      DONATION_STATUS.COMPLETED,
      DONATION_STATUS.STORED, // Thêm trạng thái "Hoàn thành" mới
      REQUEST_STATUS.COMPLETED,
      REQUEST_STATUS.FULFILLED,
    ].includes(a.status)
  ).length;

  useEffect(() => {
    loadActivityHistory();
  }, [loadActivityHistory]);

  // Reload data khi filter thay đổi (nhưng không reload ở lần mount đầu tiên)
  useEffect(() => {
    if (isFirstMount.current) {
      isFirstMount.current = false;
      return;
    }

    console.log("Filter changed to:", filter, "- reloading data");
    loadActivityHistory();
  }, [filter, loadActivityHistory]);



  // Force refresh function to clear cache and reload
  const forceRefresh = useCallback(async () => {
    console.log("Force refreshing activity history...");

    // Clear any cached data
    setActivities([]);

    // Reload data
    await loadActivityHistory();
  }, [loadActivityHistory]);

  return {
    // Data
    activities,
    filteredActivities: getFilteredActivities(),
    loading,
    filter,
    searchId,

    // Statistics
    donationCount,
    requestCount,
    completedCount,

    // Actions
    setFilter: handleFilterChange,
    setSearchId,
    loadActivityHistory,
    forceRefresh,
    handleCancelAppointment,

    // Helpers
    getUserBloodType,
  };
};

export default useActivityData;
