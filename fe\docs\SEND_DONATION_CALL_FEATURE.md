# Tính năng Gửi Email Kêu Gọi Hiến Máu

## Tổng quan
Tính năng này cho phép quản lý gửi email kêu gọi hiến máu đến tất cả những người đủ điều kiện hiến máu chỉ với một cú nhấp chuột.

## Vị trí
- **Trang**: `EligibleDonorsPage` (`/fe/src/pages/manager/EligibleDonorsPage.jsx`)
- **Đường dẫn**: `/manager/eligible-donors`
- **Quyền truy cập**: Chỉ dành cho Manager

## Tính năng chính

### 1. Button Gửi Email Kêu Gọi
- **Vị trí**: Bên cạnh bộ lọc "Sắp xếp", trong phần filters
- **Thiết kế**: Button màu đỏ cam với gradient, có icon email
- **Text**: "📧 Gửi email kêu gọ<PERSON> (X người)"
- **Trạng thái**: 
  - Disabled khi không có người nào đủ điều kiện
  - Loading state khi đang gửi email
  - Hiển thị số lượng người sẽ nhận email

### 2. Modal Xác Nhận Chi Tiết
Khi nhấn button, hệ thống hiển thị modal xác nhận với:

#### Tiêu chí gửi email:
- ✅ Đã hoàn thành quy trình hiến máu (trạng thái "Đã nhập kho")
- ✅ Đã hiến máu cách đây ít nhất 84 ngày
- ✅ Phù hợp với các bộ lọc hiện tại

#### Bộ lọc hiện tại:
- Nhóm máu đã chọn
- Khoảng cách tối đa
- Từ khóa tìm kiếm (nếu có)
- Cách sắp xếp

#### Thông tin tổng kết:
- Số lượng người sẽ nhận email (highlight)
- Nút "Gửi email" và "Hủy"

### 3. Thông Báo Kết Quả
- **Thành công**: "✅ Đã gửi email kêu gọi hiến máu đến X người đủ điều kiện!"
- **Lỗi API**: "⚠️ API gửi email chưa sẵn sàng. Vui lòng thử lại sau."
- **Lỗi khác**: "❌ Có lỗi xảy ra khi gửi email kêu gọi hiến máu!"

## API Endpoint

### Gửi Email Kêu Gọi
```
POST /api/Appointment/send-donation-call
```

**Headers:**
```
Content-Type: application/json
Authorization: Bearer {token}
```

**Request Body:**
```
Không có parameters (empty body)
```

**Response:**
```
Status: 200 OK
Content-Type: text/plain; charset=utf-8
Body: "Đã gửi email kêu gọi hiến máu."
```

## Cách Sử Dụng

### Bước 1: Truy cập trang
1. Đăng nhập với quyền Manager
2. Vào menu "Quản lý người hiến" → "Người đủ điều kiện hiến lại"

### Bước 2: Áp dụng bộ lọc (tùy chọn)
1. **Nhóm máu**: Chọn nhóm máu cần thiết hoặc "Tất cả"
2. **Khoảng cách**: Điều chỉnh khoảng cách tối đa (≤ 5km đến ≤ 100km)
3. **Tìm kiếm**: Nhập tên, số điện thoại hoặc email
4. **Sắp xếp**: Chọn cách sắp xếp danh sách

### Bước 3: Kiểm tra danh sách
- Xem số lượng người đủ điều kiện trong button
- Đảm bảo danh sách phù hợp với yêu cầu

### Bước 4: Gửi email
1. Nhấn button "📧 Gửi email kêu gọi (X người)"
2. Đọc thông tin trong modal xác nhận:
   - Kiểm tra tiêu chí gửi email
   - Xem bộ lọc hiện tại
   - Xác nhận số lượng người nhận
3. Nhấn "Gửi email" để xác nhận hoặc "Hủy" để hủy bỏ

### Bước 5: Theo dõi kết quả
- Chờ thông báo kết quả
- Button sẽ hiển thị loading trong quá trình gửi
- Kiểm tra thông báo thành công/lỗi

## Điều Kiện Gửi Email

### Người nhận phải đáp ứng TẤT CẢ các điều kiện sau:
1. **Hoàn thành quy trình**: `process = 5` (Đã nhập kho)
2. **Thời gian đủ**: Đã hiến máu cách đây ≥ 84 ngày
3. **Phù hợp bộ lọc**: Thỏa mãn các tiêu chí lọc hiện tại

### Bộ lọc hỗ trợ:
- **Nhóm máu**: O+, O-, A+, A-, B+, B-, AB+, AB-, Tất cả
- **Khoảng cách**: ≤ 5km, ≤ 10km, ≤ 20km, ≤ 50km, ≤ 100km
- **Tìm kiếm**: Tên, số điện thoại, email (không phân biệt hoa thường)
- **Sắp xếp**: Khoảng cách, Lần hiến cuối, Tên A-Z

## Files Liên Quan

### Core Files
- `fe/src/pages/manager/EligibleDonorsPage.jsx` - Trang chính chứa button và logic
- `fe/src/services/bloodDonationService.js` - Service chứa method `sendDonationCall()`

### Test Files
- `fe/src/test/sendDonationCallTest.js` - File test tính năng

### Configuration
- `fe/.env.local` - Cấu hình API base URL
- `fe/src/config/environment.js` - Cấu hình môi trường

## Troubleshooting

### Button bị disabled
- **Nguyên nhân**: Không có người nào đủ điều kiện
- **Giải pháp**: 
  - Kiểm tra bộ lọc có quá nghiêm ngặt không
  - Đảm bảo có dữ liệu người hiến trong database
  - Xác nhận điều kiện 84 ngày và process = 5

### Lỗi "API endpoint chưa sẵn sàng"
- **Nguyên nhân**: Backend chưa chạy hoặc endpoint chưa được implement
- **Giải pháp**:
  - Kiểm tra backend có chạy không
  - Xác nhận endpoint `POST /api/Appointment/send-donation-call` đã được implement
  - Kiểm tra authentication token

### Button loading mãi không dừng
- **Nguyên nhân**: API call bị timeout hoặc lỗi network
- **Giải pháp**:
  - Refresh trang
  - Kiểm tra kết nối mạng
  - Kiểm tra console để xem lỗi chi tiết

### Email không được gửi
- **Nguyên nhân**: Backend có thể đã nhận request nhưng email service có vấn đề
- **Giải pháp**:
  - Kiểm tra logs backend
  - Xác nhận email service configuration
  - Kiểm tra SMTP settings

## Lưu Ý Quan Trọng

1. **Không spam**: Button có loading state để tránh gửi email trùng lặp
2. **Bộ lọc**: Email chỉ gửi cho những người phù hợp với bộ lọc hiện tại
3. **Điều kiện 84 ngày**: Đây là quy định y tế, không thể thay đổi
4. **Quyền truy cập**: Chỉ Manager mới có thể sử dụng tính năng này
5. **Backend dependency**: Tính năng phụ thuộc hoàn toàn vào backend API

## Testing

Để test tính năng, sử dụng file test:
```javascript
// Trong browser console
import { runAllTests } from './src/test/sendDonationCallTest.js';
runAllTests();
```

Hoặc sử dụng window object (nếu đã import):
```javascript
window.testSendDonationCall.runAllTests();
```
