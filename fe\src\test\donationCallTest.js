/**
 * Test file for donation call email functionality
 * This file tests the email sending feature in EligibleDonorsPage
 */

import bloodDonationService from '../services/bloodDonationService';

// Test the sendDonationCall method
async function testSendDonationCall() {
  console.log('🧪 Testing sendDonationCall method...');
  
  try {
    // Test the sendDonationCall method
    const result = await bloodDonationService.sendDonationCall();
    console.log('✅ Donation call test passed:', result);
    return {
      success: true,
      result: result,
      message: 'Email sent successfully'
    };
  } catch (error) {
    console.log('⚠️ Donation call test failed (expected if API not ready):', error.message);
    return {
      success: false,
      error: error.message,
      message: 'API not ready or error occurred'
    };
  }
}

// Mock function to simulate button click
function simulateButtonClick() {
  console.log('🖱️ Simulating button click...');
  
  // This would normally be called by the button onClick handler
  const mockFilteredDonors = [
    { id: 1, name: '<PERSON><PERSON><PERSON><PERSON>', bloodType: 'O+' },
    { id: 2, name: '<PERSON><PERSON><PERSON><PERSON>', bloodType: 'A+' },
    { id: 3, name: '<PERSON><PERSON>', bloodType: 'B+' }
  ];
  
  console.log(`📊 Mock data: ${mockFilteredDonors.length} eligible donors`);
  
  return testSendDonationCall();
}

// Test API endpoint availability
async function testAPIEndpoint() {
  console.log('🔍 Testing API endpoint availability...');
  
  try {
    const response = await fetch('https://localhost:7021/api/Appointment/send-donation-call', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Accept': '*/*'
      }
    });
    
    if (response.ok) {
      const result = await response.text();
      console.log('✅ API endpoint is available:', result);
      return {
        available: true,
        status: response.status,
        message: result
      };
    } else {
      console.log('⚠️ API endpoint returned error:', response.status);
      return {
        available: false,
        status: response.status,
        message: 'API returned error status'
      };
    }
  } catch (error) {
    console.log('❌ API endpoint not available:', error.message);
    return {
      available: false,
      error: error.message,
      message: 'Cannot connect to API'
    };
  }
}

// Run all tests
export async function runDonationCallTests() {
  console.log('🚀 Starting donation call tests...');
  
  const apiTest = await testAPIEndpoint();
  const serviceTest = await testSendDonationCall();
  const buttonTest = await simulateButtonClick();
  
  console.log('📊 Test Results:');
  console.log(`- API Endpoint: ${apiTest.available ? 'AVAILABLE' : 'NOT AVAILABLE'}`);
  console.log(`- Service Method: ${serviceTest.success ? 'PASS' : 'FAIL'}`);
  console.log(`- Button Simulation: ${buttonTest.success ? 'PASS' : 'FAIL'}`);
  
  return {
    api: apiTest,
    service: serviceTest,
    button: buttonTest
  };
}

// Export individual test functions
export {
  testSendDonationCall,
  simulateButtonClick,
  testAPIEndpoint
};

// Auto-run tests if this file is executed directly
if (typeof window !== 'undefined' && window.location) {
  // Browser environment - can run tests
  console.log('🌐 Browser environment detected. Tests can be run manually.');
} else {
  // Node environment - run tests automatically
  runDonationCallTests().then(results => {
    console.log('🏁 All tests completed:', results);
  });
}
